[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BobineChart.js": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\CaviStatoChart.js": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "70", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "77", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "78", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "83", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "90"}, {"size": 557, "mtime": 1746952718482, "results": "91", "hashOfConfig": "92"}, {"size": 2728, "mtime": 1746952740200, "results": "93", "hashOfConfig": "92"}, {"size": 996, "mtime": 1746970152489, "results": "94", "hashOfConfig": "92"}, {"size": 10788, "mtime": 1746864244183, "results": "95", "hashOfConfig": "92"}, {"size": 18405, "mtime": 1748034045686, "results": "96", "hashOfConfig": "92"}, {"size": 6001, "mtime": 1748200429907, "results": "97", "hashOfConfig": "92"}, {"size": 2216, "mtime": 1746640055487, "results": "98", "hashOfConfig": "92"}, {"size": 7394, "mtime": 1748034003517, "results": "99", "hashOfConfig": "92"}, {"size": 6749, "mtime": 1746282201800, "results": "100", "hashOfConfig": "92"}, {"size": 21982, "mtime": 1748068137555, "results": "101", "hashOfConfig": "92"}, {"size": 2535, "mtime": 1746647873596, "results": "102", "hashOfConfig": "92"}, {"size": 2050, "mtime": 1746647945415, "results": "103", "hashOfConfig": "92"}, {"size": 700, "mtime": 1747545501078, "results": "104", "hashOfConfig": "92"}, {"size": 17518, "mtime": 1748664526035, "results": "105", "hashOfConfig": "92"}, {"size": 3999, "mtime": 1746943038491, "results": "106", "hashOfConfig": "92"}, {"size": 4001, "mtime": 1748121689116, "results": "107", "hashOfConfig": "92"}, {"size": 1630, "mtime": 1746336079554, "results": "108", "hashOfConfig": "92"}, {"size": 3070, "mtime": 1746637986362, "results": "109", "hashOfConfig": "92"}, {"size": 38714, "mtime": 1748371275138, "results": "110", "hashOfConfig": "92"}, {"size": 324, "mtime": 1748638525632, "results": "111", "hashOfConfig": "92"}, {"size": 9068, "mtime": 1746856425683, "results": "112", "hashOfConfig": "92"}, {"size": 2210, "mtime": 1747432283057, "results": "113", "hashOfConfig": "92"}, {"size": 4494, "mtime": 1748121063631, "results": "114", "hashOfConfig": "92"}, {"size": 63666, "mtime": 1748380024315, "results": "115", "hashOfConfig": "92"}, {"size": 3969, "mtime": 1746864496870, "results": "116", "hashOfConfig": "92"}, {"size": 3609, "mtime": 1746944025177, "results": "117", "hashOfConfig": "92"}, {"size": 4142, "mtime": 1746942978805, "results": "118", "hashOfConfig": "92"}, {"size": 3986, "mtime": 1746864510624, "results": "119", "hashOfConfig": "92"}, {"size": 3973, "mtime": 1746864489032, "results": "120", "hashOfConfig": "92"}, {"size": 2975, "mtime": 1747554796402, "results": "121", "hashOfConfig": "92"}, {"size": 3429, "mtime": 1747721794176, "results": "122", "hashOfConfig": "92"}, {"size": 3109, "mtime": 1747824114392, "results": "123", "hashOfConfig": "92"}, {"size": 2929, "mtime": 1747655572696, "results": "124", "hashOfConfig": "92"}, {"size": 3302, "mtime": 1748000902435, "results": "125", "hashOfConfig": "92"}, {"size": 5597, "mtime": 1748070089791, "results": "126", "hashOfConfig": "92"}, {"size": 5880, "mtime": 1748121404574, "results": "127", "hashOfConfig": "92"}, {"size": 3447, "mtime": 1748664345657, "results": "128", "hashOfConfig": "92"}, {"size": 4720, "mtime": 1746771178920, "results": "129", "hashOfConfig": "92"}, {"size": 7121, "mtime": 1746281148395, "results": "130", "hashOfConfig": "92"}, {"size": 7958, "mtime": 1746280443400, "results": "131", "hashOfConfig": "92"}, {"size": 6259, "mtime": 1746965906057, "results": "132", "hashOfConfig": "92"}, {"size": 4215, "mtime": 1746278746358, "results": "133", "hashOfConfig": "92"}, {"size": 1273, "mtime": 1746809069006, "results": "134", "hashOfConfig": "92"}, {"size": 14270, "mtime": 1748371983481, "results": "135", "hashOfConfig": "92"}, {"size": 2752, "mtime": 1747022186740, "results": "136", "hashOfConfig": "92"}, {"size": 1072, "mtime": 1746637929350, "results": "137", "hashOfConfig": "92"}, {"size": 6745, "mtime": 1747545492454, "results": "138", "hashOfConfig": "92"}, {"size": 38569, "mtime": 1748371531457, "results": "139", "hashOfConfig": "92"}, {"size": 23333, "mtime": 1746463652843, "results": "140", "hashOfConfig": "92"}, {"size": 47271, "mtime": 1748072224692, "results": "141", "hashOfConfig": "92"}, {"size": 38669, "mtime": 1748199713253, "results": "142", "hashOfConfig": "92"}, {"size": 1947, "mtime": 1748120984640, "results": "143", "hashOfConfig": "92"}, {"size": 54895, "mtime": 1748370360136, "results": "144", "hashOfConfig": "92"}, {"size": 11421, "mtime": 1748164759425, "results": "145", "hashOfConfig": "92"}, {"size": 9249, "mtime": 1747491620193, "results": "146", "hashOfConfig": "92"}, {"size": 11771, "mtime": 1746948731812, "results": "147", "hashOfConfig": "92"}, {"size": 4056, "mtime": 1748067890376, "results": "148", "hashOfConfig": "92"}, {"size": 16522, "mtime": 1748379859408, "results": "149", "hashOfConfig": "92"}, {"size": 10993, "mtime": 1747154871546, "results": "150", "hashOfConfig": "92"}, {"size": 12150, "mtime": 1748205557322, "results": "151", "hashOfConfig": "92"}, {"size": 7185, "mtime": 1748205816300, "results": "152", "hashOfConfig": "92"}, {"size": 8016, "mtime": 1748205755456, "results": "153", "hashOfConfig": "92"}, {"size": 12306, "mtime": 1748205594594, "results": "154", "hashOfConfig": "92"}, {"size": 7032, "mtime": 1748069273238, "results": "155", "hashOfConfig": "92"}, {"size": 8589, "mtime": 1748207111023, "results": "156", "hashOfConfig": "92"}, {"size": 9979, "mtime": 1748069243848, "results": "157", "hashOfConfig": "92"}, {"size": 10821, "mtime": 1748069202177, "results": "158", "hashOfConfig": "92"}, {"size": 36555, "mtime": 1747684003188, "results": "159", "hashOfConfig": "92"}, {"size": 9483, "mtime": 1747194869458, "results": "160", "hashOfConfig": "92"}, {"size": 13900, "mtime": 1748182219170, "results": "161", "hashOfConfig": "92"}, {"size": 48588, "mtime": 1747948123233, "results": "162", "hashOfConfig": "92"}, {"size": 92270, "mtime": 1748123070273, "results": "163", "hashOfConfig": "92"}, {"size": 522, "mtime": 1747022186711, "results": "164", "hashOfConfig": "92"}, {"size": 6612, "mtime": 1748069456201, "results": "165", "hashOfConfig": "92"}, {"size": 3796, "mtime": 1747022186720, "results": "166", "hashOfConfig": "92"}, {"size": 1703, "mtime": 1746972529152, "results": "167", "hashOfConfig": "92"}, {"size": 19892, "mtime": 1747554544219, "results": "168", "hashOfConfig": "92"}, {"size": 12050, "mtime": 1747547543421, "results": "169", "hashOfConfig": "92"}, {"size": 1686, "mtime": 1746946499500, "results": "170", "hashOfConfig": "92"}, {"size": 5145, "mtime": 1746914029633, "results": "171", "hashOfConfig": "92"}, {"size": 9788, "mtime": 1747491601484, "results": "172", "hashOfConfig": "92"}, {"size": 22179, "mtime": 1747432554979, "results": "173", "hashOfConfig": "92"}, {"size": 2258, "mtime": 1746946368534, "results": "174", "hashOfConfig": "92"}, {"size": 4094, "mtime": 1748161663641, "results": "175", "hashOfConfig": "92"}, {"size": 5273, "mtime": 1747946737459, "results": "176", "hashOfConfig": "92"}, {"size": 4346, "mtime": 1747491472989, "results": "177", "hashOfConfig": "92"}, {"size": 15571, "mtime": 1747980774491, "results": "178", "hashOfConfig": "92"}, {"size": 7839, "mtime": 1748664451344, "results": "179", "hashOfConfig": "92"}, {"size": 6529, "mtime": 1748664406267, "results": "180", "hashOfConfig": "92"}, {"size": 13270, "mtime": 1748664384742, "results": "181", "hashOfConfig": "92"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["452", "453", "454", "455"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["456", "457", "458", "459", "460", "461", "462", "463", "464", "465"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["466"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["467", "468", "469", "470", "471", "472", "473", "474", "475", "476"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", ["492", "493", "494", "495"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["496"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["497", "498"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["514"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["515"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["532", "533"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["534", "535"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["536", "537"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["538"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["539", "540", "541", "542", "543", "544", "545"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["546", "547", "548"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["549", "550", "551", "552", "553", "554", "555"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["556", "557"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["558", "559"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["560", "561"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["562", "563"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["564", "565", "566", "567", "568", "569", "570", "571"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", ["572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["598", "599", "600", "601", "602", "603", "604"], ["605"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["629", "630"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["631", "632"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["646"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["647", "648", "649", "650", "651", "652"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BobineChart.js", ["653", "654", "655", "656"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\CaviStatoChart.js", ["657", "658"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["659"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["660"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["661"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["673"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["674", "675", "676", "677"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["718", "719"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["720", "721"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["722", "723"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["744", "745", "746", "747", "748", "749", "750", "751"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["752"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["753", "754", "755"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["756"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["757"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], {"ruleId": "758", "severity": 1, "message": "759", "line": 78, "column": 11, "nodeType": "760", "messageId": "761", "endLine": 78, "endColumn": 115}, {"ruleId": "758", "severity": 1, "message": "759", "line": 80, "column": 11, "nodeType": "760", "messageId": "761", "endLine": 80, "endColumn": 107}, {"ruleId": "758", "severity": 1, "message": "759", "line": 86, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 86, "endColumn": 105}, {"ruleId": "758", "severity": 1, "message": "759", "line": 89, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 89, "endColumn": 41}, {"ruleId": "762", "severity": 1, "message": "763", "line": 13, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "766", "line": 20, "column": 25, "nodeType": "764", "messageId": "765", "endLine": 20, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "767", "line": 21, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 35}, {"ruleId": "762", "severity": 1, "message": "768", "line": 22, "column": 12, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "769", "line": 23, "column": 18, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 28}, {"ruleId": "762", "severity": 1, "message": "770", "line": 56, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 56, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "771", "line": 57, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 57, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "772", "line": 58, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 58, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "773", "line": 59, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 59, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "774", "line": 68, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 68, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "775", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "776", "line": 2, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 31}, {"ruleId": "762", "severity": 1, "message": "777", "line": 2, "column": 33, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 37}, {"ruleId": "762", "severity": 1, "message": "778", "line": 2, "column": 39, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 50}, {"ruleId": "762", "severity": 1, "message": "779", "line": 2, "column": 52, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 66}, {"ruleId": "762", "severity": 1, "message": "763", "line": 2, "column": 68, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 74}, {"ruleId": "762", "severity": 1, "message": "766", "line": 5, "column": 25, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "767", "line": 6, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 35}, {"ruleId": "762", "severity": 1, "message": "768", "line": 7, "column": 12, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "769", "line": 8, "column": 18, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 28}, {"ruleId": "762", "severity": 1, "message": "780", "line": 43, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "777", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "778", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "781", "line": 11, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "776", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "782", "line": 13, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "783", "line": 18, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "784", "line": 19, "column": 15, "nodeType": "764", "messageId": "765", "endLine": 19, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "785", "line": 20, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 20, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "786", "line": 21, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "787", "line": 22, "column": 13, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "788", "line": 23, "column": 14, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 25}, {"ruleId": "762", "severity": 1, "message": "789", "line": 28, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "790", "line": 31, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "791", "line": 51, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 51, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "792", "line": 56, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 56, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "793", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "783", "line": 13, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "790", "line": 21, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "794", "line": 28, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "795", "line": 11, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "783", "line": 13, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "790", "line": 21, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "777", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "778", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "796", "line": 11, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "797", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "798", "line": 25, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 25, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "799", "line": 32, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 32, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "790", "line": 38, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 38, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "800", "line": 40, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 40, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "794", "line": 42, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 42, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "801", "line": 110, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 110, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "802", "line": 118, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 118, "endColumn": 28}, {"ruleId": "762", "severity": 1, "message": "803", "line": 119, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 119, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "804", "line": 119, "column": 25, "nodeType": "764", "messageId": "765", "endLine": 119, "endColumn": 41}, {"ruleId": "805", "severity": 1, "message": "806", "line": 473, "column": 6, "nodeType": "807", "endLine": 473, "endColumn": 15, "suggestions": "808"}, {"ruleId": "762", "severity": 1, "message": "809", "line": 478, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 478, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "810", "line": 1, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 36}, {"ruleId": "762", "severity": 1, "message": "811", "line": 49, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 49, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "777", "line": 7, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "778", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "781", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "812", "line": 16, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 16, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "813", "line": 26, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 26, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "814", "line": 27, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 27, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "815", "line": 28, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "816", "line": 35, "column": 15, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "817", "line": 42, "column": 16, "nodeType": "764", "messageId": "765", "endLine": 42, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "818", "line": 45, "column": 17, "nodeType": "764", "messageId": "765", "endLine": 45, "endColumn": 31}, {"ruleId": "762", "severity": 1, "message": "819", "line": 57, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 57, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "820", "line": 59, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 59, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "821", "line": 64, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 64, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "822", "line": 162, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 162, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "823", "line": 280, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 280, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "824", "line": 322, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 322, "endColumn": 28}, {"ruleId": "762", "severity": 1, "message": "793", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "794", "line": 31, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "793", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "794", "line": 31, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "793", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "794", "line": 31, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "794", "line": 27, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 27, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "793", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "825", "line": 6, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "783", "line": 14, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "790", "line": 23, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "794", "line": 30, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 30, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "826", "line": 33, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "827", "line": 38, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 38, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "790", "line": 20, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 20, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "794", "line": 27, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 27, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "827", "line": 35, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "793", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "825", "line": 6, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "783", "line": 14, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "790", "line": 23, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "794", "line": 30, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 30, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "826", "line": 33, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "827", "line": 38, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 38, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "790", "line": 24, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 24, "endColumn": 26}, {"ruleId": "805", "severity": 1, "message": "828", "line": 53, "column": 6, "nodeType": "807", "endLine": 53, "endColumn": 18, "suggestions": "829"}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "782", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "832", "line": 28, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 18}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "777", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "778", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "781", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "833", "line": 23, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "834", "line": 24, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 24, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "784", "line": 46, "column": 15, "nodeType": "764", "messageId": "765", "endLine": 46, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "835", "line": 47, "column": 12, "nodeType": "764", "messageId": "765", "endLine": 47, "endColumn": 21}, {"ruleId": "805", "severity": 1, "message": "836", "line": 134, "column": 6, "nodeType": "807", "endLine": 134, "endColumn": 18, "suggestions": "837"}, {"ruleId": "762", "severity": 1, "message": "777", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "778", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "781", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "833", "line": 23, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "834", "line": 24, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 24, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "782", "line": 25, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 25, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "785", "line": 37, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 37, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "838", "line": 41, "column": 13, "nodeType": "764", "messageId": "765", "endLine": 41, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "784", "line": 43, "column": 15, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "839", "line": 44, "column": 17, "nodeType": "764", "messageId": "765", "endLine": 44, "endColumn": 31}, {"ruleId": "805", "severity": 1, "message": "840", "line": 98, "column": 6, "nodeType": "807", "endLine": 98, "endColumn": 18, "suggestions": "841"}, {"ruleId": "762", "severity": 1, "message": "842", "line": 101, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 101, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "777", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "778", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "781", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "833", "line": 23, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "834", "line": 24, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 24, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "782", "line": 25, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 25, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "796", "line": 29, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 29, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "786", "line": 39, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 39, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "784", "line": 43, "column": 15, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "843", "line": 44, "column": 14, "nodeType": "764", "messageId": "765", "endLine": 44, "endColumn": 25}, {"ruleId": "762", "severity": 1, "message": "844", "line": 50, "column": 69, "nodeType": "764", "messageId": "765", "endLine": 50, "endColumn": 76}, {"ruleId": "762", "severity": 1, "message": "845", "line": 79, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 79, "endColumn": 26}, {"ruleId": "805", "severity": 1, "message": "846", "line": 145, "column": 6, "nodeType": "807", "endLine": 145, "endColumn": 8, "suggestions": "847"}, {"ruleId": "762", "severity": 1, "message": "848", "line": 689, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 689, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "849", "line": 20, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 20, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "850", "line": 21, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "851", "line": 22, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "776", "line": 23, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "852", "line": 26, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 26, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "853", "line": 69, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 69, "endColumn": 22}, {"ruleId": "854", "severity": 1, "message": "855", "line": 466, "column": 9, "nodeType": "856", "messageId": "857", "endLine": 469, "endColumn": 10}, {"ruleId": "805", "severity": 1, "message": "858", "line": 95, "column": 6, "nodeType": "807", "endLine": 95, "endColumn": 21, "suggestions": "859", "suppressions": "860"}, {"ruleId": "758", "severity": 1, "message": "759", "line": 260, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 264, "endColumn": 11}, {"ruleId": "758", "severity": 1, "message": "759", "line": 274, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 274, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 278, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 278, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 333, "column": 11, "nodeType": "760", "messageId": "761", "endLine": 338, "endColumn": 13}, {"ruleId": "758", "severity": 1, "message": "759", "line": 435, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 439, "endColumn": 11}, {"ruleId": "758", "severity": 1, "message": "759", "line": 451, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 451, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 668, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 668, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 677, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 677, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 681, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 681, "endColumn": 54}, {"ruleId": "762", "severity": 1, "message": "861", "line": 755, "column": 17, "nodeType": "764", "messageId": "765", "endLine": 755, "endColumn": 22}, {"ruleId": "758", "severity": 1, "message": "759", "line": 775, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 779, "endColumn": 11}, {"ruleId": "758", "severity": 1, "message": "759", "line": 794, "column": 11, "nodeType": "760", "messageId": "761", "endLine": 798, "endColumn": 13}, {"ruleId": "758", "severity": 1, "message": "759", "line": 801, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 804, "endColumn": 11}, {"ruleId": "758", "severity": 1, "message": "759", "line": 810, "column": 11, "nodeType": "760", "messageId": "761", "endLine": 814, "endColumn": 13}, {"ruleId": "758", "severity": 1, "message": "759", "line": 817, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 820, "endColumn": 11}, {"ruleId": "758", "severity": 1, "message": "759", "line": 885, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 889, "endColumn": 11}, {"ruleId": "862", "severity": 1, "message": "863", "line": 955, "column": 3, "nodeType": "864", "messageId": "865", "endLine": 955, "endColumn": 29}, {"ruleId": "862", "severity": 1, "message": "866", "line": 1143, "column": 3, "nodeType": "864", "messageId": "865", "endLine": 1143, "endColumn": 23}, {"ruleId": "862", "severity": 1, "message": "867", "line": 1238, "column": 3, "nodeType": "864", "messageId": "865", "endLine": 1238, "endColumn": 20}, {"ruleId": "758", "severity": 1, "message": "759", "line": 1287, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 1287, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 1317, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 1317, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 1370, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 1370, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 1412, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 1412, "endColumn": 163}, {"ruleId": "762", "severity": 1, "message": "868", "line": 6, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "782", "line": 11, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "869", "line": 6, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "870", "line": 7, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 6}, {"ruleId": "762", "severity": 1, "message": "871", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "872", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "873", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "874", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "875", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "876", "line": 15, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "877", "line": 49, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 49, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "878", "line": 63, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 63, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "879", "line": 79, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 79, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "880", "line": 84, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 84, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "881", "line": 100, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 100, "endColumn": 30}, {"ruleId": "762", "severity": 1, "message": "875", "line": 3, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 3, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "882", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "876", "line": 15, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "875", "line": 16, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 16, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "883", "line": 18, "column": 40, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 44}, {"ruleId": "762", "severity": 1, "message": "884", "line": 44, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 44, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "885", "line": 65, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 65, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "886", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "887", "line": 15, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "888", "line": 82, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 82, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "889", "line": 106, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 106, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "882", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "876", "line": 15, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "793", "line": 3, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 3, "endColumn": 8}, {"ruleId": "805", "severity": 1, "message": "890", "line": 54, "column": 6, "nodeType": "807", "endLine": 54, "endColumn": 34, "suggestions": "891"}, {"ruleId": "762", "severity": 1, "message": "892", "line": 25, "column": 13, "nodeType": "764", "messageId": "765", "endLine": 25, "endColumn": 25}, {"ruleId": "762", "severity": 1, "message": "893", "line": 33, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "894", "line": 34, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 34, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "895", "line": 35, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "896", "line": 36, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 36, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "897", "line": 37, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 37, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "898", "line": 41, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 41, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "899", "line": 43, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "900", "line": 69, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 69, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "901", "line": 69, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 69, "endColumn": 29}, {"ruleId": "805", "severity": 1, "message": "902", "line": 88, "column": 6, "nodeType": "807", "endLine": 88, "endColumn": 18, "suggestions": "903"}, {"ruleId": "805", "severity": 1, "message": "904", "line": 448, "column": 6, "nodeType": "807", "endLine": 448, "endColumn": 28, "suggestions": "905"}, {"ruleId": "762", "severity": 1, "message": "906", "line": 4, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 4, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "907", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 7}, {"ruleId": "762", "severity": 1, "message": "908", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "909", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 15}, {"ruleId": "805", "severity": 1, "message": "890", "line": 46, "column": 6, "nodeType": "807", "endLine": 46, "endColumn": 18, "suggestions": "910"}, {"ruleId": "762", "severity": 1, "message": "911", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "849", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "850", "line": 11, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "851", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "909", "line": 33, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "912", "line": 35, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "843", "line": 42, "column": 14, "nodeType": "764", "messageId": "765", "endLine": 42, "endColumn": 25}, {"ruleId": "762", "severity": 1, "message": "893", "line": 52, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 52, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "894", "line": 53, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 53, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "895", "line": 54, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 54, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "896", "line": 55, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 55, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "897", "line": 56, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 56, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "913", "line": 57, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 57, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "914", "line": 58, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 58, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "915", "line": 59, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 59, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "800", "line": 72, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 72, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "916", "line": 79, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 79, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "917", "line": 79, "column": 25, "nodeType": "764", "messageId": "765", "endLine": 79, "endColumn": 41}, {"ruleId": "762", "severity": 1, "message": "918", "line": 80, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 80, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "919", "line": 85, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 85, "endColumn": 26}, {"ruleId": "805", "severity": 1, "message": "890", "line": 105, "column": 6, "nodeType": "807", "endLine": 105, "endColumn": 18, "suggestions": "920"}, {"ruleId": "805", "severity": 1, "message": "921", "line": 112, "column": 6, "nodeType": "807", "endLine": 112, "endColumn": 20, "suggestions": "922"}, {"ruleId": "805", "severity": 1, "message": "923", "line": 127, "column": 6, "nodeType": "807", "endLine": 127, "endColumn": 34, "suggestions": "924"}, {"ruleId": "762", "severity": 1, "message": "925", "line": 283, "column": 13, "nodeType": "764", "messageId": "765", "endLine": 283, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "852", "line": 17, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 17, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "909", "line": 34, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 34, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "912", "line": 35, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "926", "line": 39, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 39, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "893", "line": 51, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 51, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "894", "line": 52, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 52, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "896", "line": 54, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 54, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "897", "line": 55, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 55, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "899", "line": 62, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 62, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "927", "line": 105, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 105, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "928", "line": 105, "column": 28, "nodeType": "764", "messageId": "765", "endLine": 105, "endColumn": 47}, {"ruleId": "805", "severity": 1, "message": "921", "line": 145, "column": 6, "nodeType": "807", "endLine": 145, "endColumn": 18, "suggestions": "929"}, {"ruleId": "762", "severity": 1, "message": "930", "line": 701, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 701, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "931", "line": 1311, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 1311, "endColumn": 28}, {"ruleId": "762", "severity": 1, "message": "932", "line": 1316, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 1316, "endColumn": 30}, {"ruleId": "762", "severity": 1, "message": "933", "line": 1883, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 1883, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "830", "line": 1, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "831", "line": 5, "column": 7, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "934", "line": 83, "column": 13, "nodeType": "764", "messageId": "765", "endLine": 83, "endColumn": 21}, {"ruleId": "758", "severity": 1, "message": "759", "line": 109, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 109, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 123, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 123, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 127, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 127, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 212, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 212, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 226, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 226, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 230, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 230, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 271, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 271, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 280, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 280, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 284, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 284, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 320, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 320, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 324, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 324, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 360, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 360, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 369, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 369, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 373, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 373, "endColumn": 54}, {"ruleId": "758", "severity": 1, "message": "759", "line": 450, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 450, "endColumn": 163}, {"ruleId": "758", "severity": 1, "message": "759", "line": 459, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 459, "endColumn": 70}, {"ruleId": "758", "severity": 1, "message": "759", "line": 463, "column": 9, "nodeType": "760", "messageId": "761", "endLine": 463, "endColumn": 54}, {"ruleId": "762", "severity": 1, "message": "935", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "785", "line": 27, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 27, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "798", "line": 30, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 30, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "895", "line": 34, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 34, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "900", "line": 49, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 49, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "901", "line": 49, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 49, "endColumn": 29}, {"ruleId": "805", "severity": 1, "message": "890", "line": 64, "column": 6, "nodeType": "807", "endLine": 64, "endColumn": 32, "suggestions": "936"}, {"ruleId": "762", "severity": 1, "message": "937", "line": 270, "column": 17, "nodeType": "764", "messageId": "765", "endLine": 270, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "938", "line": 17, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 17, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "851", "line": 16, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 16, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "850", "line": 17, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 17, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "849", "line": 19, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 19, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "798", "line": 14, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "796", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 13}, "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "no-unused-vars", "'Avatar' is defined but never used.", "Identifier", "unusedVar", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'CardActions' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'Paper' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'IconButton' is defined but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["939"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'PieChartIcon' is defined but never used.", "'DateRangeIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'BoqChart' is defined but never used.", "'CaviStatoChart' is defined but never used.", "'user' is assigned a value but never used.", "'reportTypes' is assigned a value but never used.", "'handleReportSelect' is assigned a value but never used.", "'renderReportContent' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["940"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["941"], "'SearchIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array.", ["942"], "'handleOptionSelect' is assigned a value but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["943"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["944"], ["945"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Legend' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'posaTrendData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'ComposedChart' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'ScatterChart' is defined but never used.", "'Scatter' is defined but never used.", "'efficienzaData' is assigned a value but never used.", "'ScatterTooltip' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["946"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["947"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["948"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["949"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["950"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["951"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["952"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["953"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["954"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", {"desc": "955", "fix": "956"}, {"desc": "957", "fix": "958"}, {"desc": "959", "fix": "960"}, {"desc": "961", "fix": "962"}, {"desc": "963", "fix": "964"}, {"desc": "965", "fix": "966"}, {"kind": "967", "justification": "968"}, {"desc": "969", "fix": "970"}, {"desc": "971", "fix": "972"}, {"desc": "973", "fix": "974"}, {"desc": "975", "fix": "976"}, {"desc": "975", "fix": "977"}, {"desc": "978", "fix": "979"}, {"desc": "980", "fix": "981"}, {"desc": "982", "fix": "983"}, {"desc": "984", "fix": "985"}, "Update the dependencies array to be: [error, filters, user]", {"range": "986", "text": "987"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "988", "text": "989"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "990", "text": "991"}, "Update the dependencies array to be: [cantiereId, loadComande]", {"range": "992", "text": "993"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "994", "text": "995"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "996", "text": "997"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "998", "text": "999"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1000", "text": "1001"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1002", "text": "1003"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1004", "text": "1005"}, {"range": "1006", "text": "1005"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1007", "text": "1008"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1009", "text": "1010"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1011", "text": "1012"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1013", "text": "1014"}, [19784, 19793], "[error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [2335, 2347], "[cantiereId, loadComande]", [4642, 4644], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]"]