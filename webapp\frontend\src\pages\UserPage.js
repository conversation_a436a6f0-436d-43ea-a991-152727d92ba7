import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Snackbar,
  Alert,
  IconButton,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Construction as ConstructionIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  ContentCopy as ContentCopyIcon,
  Info as InfoIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon
} from '@mui/icons-material';
import CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import cantieriService from '../services/cantieriService';
import './UserPage.css';

const UserPage = () => {
  const { user, isImpersonating, impersonatedU<PERSON>, selectCantiere } = useAuth();
  const navigate = useNavigate();
  const [cantieri, setCantieri] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedCantiere, setSelectedCantiere] = useState(null);
  const [newCantiereData, setNewCantiereData] = useState({
    nome: '',
    descrizione: '',
    password_cantiere: '',
    conferma_password: ''
  });
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'

  // Carica i cantieri dell'utente corrente o dell'utente impersonato
  useEffect(() => {
    const fetchCantieri = async () => {
      try {
        setLoading(true);
        let data;

        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente
        if (user?.role === 'owner' && isImpersonating && impersonatedUser) {
          // Carica i cantieri dell'utente impersonato
          data = await cantieriService.getUserCantieri(impersonatedUser.id);
        } else {
          // Altrimenti carica i cantieri dell'utente corrente
          data = await cantieriService.getMyCantieri();
        }

        setCantieri(data);
      } catch (err) {
        console.error('Errore nel caricamento dei cantieri:', err);
        setError('Impossibile caricare i cantieri. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    fetchCantieri();
  }, [user, isImpersonating, impersonatedUser]);

  // Gestisce l'apertura del dialog per creare un nuovo cantiere
  const handleOpenCreateDialog = () => {
    setNewCantiereData({
      nome: '',
      descrizione: '',
      password_cantiere: '',
      conferma_password: ''
    });
    setOpenCreateDialog(true);
  };

  // Gestisce la chiusura del dialog per creare un nuovo cantiere
  const handleCloseCreateDialog = () => {
    setOpenCreateDialog(false);
  };

  // Gestisce l'apertura del dialog per eliminare un cantiere
  const handleOpenDeleteDialog = (cantiere) => {
    setSelectedCantiere(cantiere);
    setOpenDeleteDialog(true);
  };

  // Gestisce la chiusura del dialog per eliminare un cantiere
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedCantiere(null);
  };

  // Gestisce la modifica dei campi del form per creare un nuovo cantiere
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewCantiereData({
      ...newCantiereData,
      [name]: value
    });
  };

  // Gestisce la creazione di un nuovo cantiere
  const handleCreateCantiere = async () => {
    // Verifica che i campi obbligatori siano compilati
    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {
      setNotification({
        open: true,
        message: 'Il nome e la password sono obbligatori',
        severity: 'error'
      });
      return;
    }

    // Verifica che le password coincidano
    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {
      setNotification({
        open: true,
        message: 'Le password non coincidono',
        severity: 'error'
      });
      return;
    }

    try {
      const createdCantiere = await cantieriService.createCantiere({
        nome: newCantiereData.nome,
        descrizione: newCantiereData.descrizione,
        password_cantiere: newCantiereData.password_cantiere
      });

      // Aggiorna la lista dei cantieri
      setCantieri([...cantieri, createdCantiere]);

      // Chiudi il dialog
      handleCloseCreateDialog();

      // Mostra una notifica di successo con la password
      setNotification({
        open: true,
        message: `Cantiere ${createdCantiere.nome} creato con successo!\nCodice univoco: ${createdCantiere.codice_univoco}\nPassword: ${newCantiereData.password_cantiere}\n\nSalva queste informazioni in un luogo sicuro!`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Errore nella creazione del cantiere:', err);
      setNotification({
        open: true,
        message: 'Errore nella creazione del cantiere',
        severity: 'error'
      });
    }
  };

  // Gestisce l'eliminazione di un cantiere
  const handleDeleteCantiere = async () => {
    if (!selectedCantiere) return;

    try {
      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);

      // Aggiorna la lista dei cantieri
      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));

      // Chiudi il dialog
      handleCloseDeleteDialog();

      // Mostra una notifica di successo
      setNotification({
        open: true,
        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Errore nell\'eliminazione del cantiere:', err);
      setNotification({
        open: true,
        message: 'Errore nell\'eliminazione del cantiere',
        severity: 'error'
      });
    }
  };

  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi
  const handleSelectCantiere = (cantiere) => {
    console.log('Selezionato cantiere:', cantiere);

    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage
    selectCantiere(cantiere);

    // Naviga direttamente alla pagina di visualizzazione cavi
    navigate('/dashboard/cavi/visualizza');
  };

  // Gestisce la chiusura della notifica
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // Gestisce il cambio di modalità di visualizzazione
  const handleViewModeChange = (event, newViewMode) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  // Gestisce la copia del codice univoco
  const handleCopyCode = (codiceUnivoco) => {
    navigator.clipboard.writeText(codiceUnivoco);
    setNotification({
      open: true,
      message: 'Codice univoco copiato negli appunti',
      severity: 'success'
    });
  };

  return (
    <Box className="cantieri-page">
      <Typography variant="h4" gutterBottom>
        {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : "I Miei Cantieri"}
      </Typography>

      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body1">
          {isImpersonating && impersonatedUser
            ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}`
            : "Visualizza e gestisci i tuoi cantieri"}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {cantieri.length > 0 && (
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              size="small"
            >
              <ToggleButton value="table" aria-label="vista tabella">
                <ViewListIcon />
              </ToggleButton>
              <ToggleButton value="cards" aria-label="vista schede">
                <ViewModuleIcon />
              </ToggleButton>
            </ToggleButtonGroup>
          )}
          <Button
            variant="contained"
            className="primary-button"
            startIcon={<AddIcon />}
            onClick={handleOpenCreateDialog}
          >
            Nuovo Cantiere
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Typography>Caricamento cantieri...</Typography>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : cantieri.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">Nessun cantiere trovato</Typography>
          <Typography variant="body2" color="text.secondary">
            Crea un nuovo cantiere per iniziare
          </Typography>
          <Button
            variant="contained"
            className="primary-button"
            startIcon={<AddIcon />}
            onClick={handleOpenCreateDialog}
            sx={{ mt: 2 }}
          >
            Nuovo Cantiere
          </Button>
        </Paper>
      ) : viewMode === 'table' ? (
        <CantieriFilterableTable
          cantieri={cantieri}
          loading={loading}
          onManageCavi={handleSelectCantiere}
          onDelete={handleOpenDeleteDialog}
          onCopyCode={handleCopyCode}
        />
      ) : (
        <Grid container spacing={3}>
          {cantieri.map((cantiere) => (
            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>
              <Card>
                <CardContent>
                  <Box className="cantiere-header">
                    <ConstructionIcon />
                    <Typography variant="h6" component="div">
                      {cantiere.nome}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    <strong>Descrizione:</strong> {cantiere.descrizione || 'N/A'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      <strong>Password:</strong> ********
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setNotification({
                          open: true,
                          message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',
                          severity: 'info'
                        });
                      }}
                      title="Informazioni sulla password"
                    >
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      <strong>Codice Univoco:</strong> {cantiere.codice_univoco || 'N/A'}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={() => handleCopyCode(cantiere.codice_univoco)}
                      title="Copia codice univoco"
                    >
                      <ContentCopyIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </CardContent>
                <CardActions>
                  <Button
                    variant="contained"
                    className="primary-button"
                    onClick={() => handleSelectCantiere(cantiere)}
                  >
                    Gestione Cavi
                  </Button>
                  <Button
                    variant="contained"
                    className="error-button"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleOpenDeleteDialog(cantiere)}
                  >
                    Elimina
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Dialog per creare un nuovo cantiere */}
      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>
        <DialogTitle>Crea Nuovo Cantiere</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Inserisci i dati per creare un nuovo cantiere.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            name="nome"
            label="Nome Cantiere"
            type="text"
            fullWidth
            variant="outlined"
            value={newCantiereData.nome}
            onChange={handleInputChange}
            required
          />
          <TextField
            margin="dense"
            name="descrizione"
            label="Descrizione"
            type="text"
            fullWidth
            variant="outlined"
            value={newCantiereData.descrizione}
            onChange={handleInputChange}
          />
          <TextField
            margin="dense"
            name="password_cantiere"
            label="Password"
            type="password"
            fullWidth
            variant="outlined"
            value={newCantiereData.password_cantiere}
            onChange={handleInputChange}
            required
          />
          <TextField
            margin="dense"
            name="conferma_password"
            label="Conferma Password"
            type="password"
            fullWidth
            variant="outlined"
            value={newCantiereData.conferma_password}
            onChange={handleInputChange}
            required
          />

        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={handleCloseCreateDialog}>Annulla</Button>
          <Button onClick={handleCreateCantiere} variant="contained" className="primary-button">
            Crea
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per eliminare un cantiere */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Elimina Cantiere</DialogTitle>
        <DialogContent>
          <DialogContentText>
            ATTENZIONE: Sei sicuro di voler eliminare il cantiere "{selectedCantiere?.nome}" e tutti i suoi dati?
            Questa operazione non può essere annullata.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={handleCloseDeleteDialog}>Annulla</Button>
          <Button onClick={handleDeleteCantiere} variant="contained" className="error-button">
            Elimina
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifica */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          <div style={{ whiteSpace: 'pre-line' }}>{notification.message}</div>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserPage;
