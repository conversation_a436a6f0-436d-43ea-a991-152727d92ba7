from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import List, Any, Optional
from datetime import datetime, date

from webapp.backend.database import get_db
from webapp.backend.models.user import User
from webapp.backend.models.cantiere import Cantiere
from webapp.backend.models.cavo import Cavo
from webapp.backend.models.certificazione_cavo import CertificazioneCavo
from webapp.backend.models.strumento_certificato import StrumentoCertificato
from webapp.backend.schemas.certificazione_cavo import (
    CertificazioneCavoCreate,
    CertificazioneCavoUpdate,
    CertificazioneCavoResponse,
    CertificazioneCavoListResponse
)
from webapp.backend.core.security import get_current_active_user

router = APIRouter()


def genera_numero_certificato(db: Session, id_cantiere: int) -> str:
    """
    Genera il prossimo numero di certificato per il cantiere.
    Formato: CERT0001, CERT0002, etc.
    """
    # Trova l'ultimo numero di certificato per il cantiere
    ultimo_certificato = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_cantiere == id_cantiere,
        CertificazioneCavo.numero_certificato.like('CERT%')
    ).order_by(CertificazioneCavo.numero_certificato.desc()).first()

    if ultimo_certificato:
        try:
            # Estrae il numero dalla stringa CERT0001 -> 1
            ultimo_numero = int(ultimo_certificato.numero_certificato[4:])
            nuovo_numero = ultimo_numero + 1
        except (ValueError, IndexError):
            nuovo_numero = 1
    else:
        nuovo_numero = 1

    return f"CERT{nuovo_numero:04d}"


@router.get("/{cantiere_id}/certificazioni", response_model=List[CertificazioneCavoListResponse])
def get_certificazioni_cantiere(
    cantiere_id: int,
    filtro_cavo: Optional[str] = Query(None, description="Filtro per ID cavo"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutte le certificazioni di un cantiere con possibilità di filtrare per ID cavo.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Query base con join per ottenere informazioni del cavo
    query = db.query(
        CertificazioneCavo.id_certificazione,
        CertificazioneCavo.id_cavo,
        CertificazioneCavo.numero_certificato,
        CertificazioneCavo.data_certificazione,
        CertificazioneCavo.id_operatore,
        CertificazioneCavo.valore_isolamento,
        CertificazioneCavo.strumento_utilizzato,
        CertificazioneCavo.lunghezza_misurata,
        Cavo.tipologia.label('cavo_tipologia'),
        Cavo.sezione.label('cavo_sezione')
    ).join(
        Cavo, and_(
            CertificazioneCavo.id_cavo == Cavo.id_cavo,
            CertificazioneCavo.id_cantiere == Cavo.id_cantiere
        )
    ).filter(CertificazioneCavo.id_cantiere == cantiere_id)

    # Applica filtro se specificato
    if filtro_cavo:
        query = query.filter(CertificazioneCavo.id_cavo.like(f"%{filtro_cavo}%"))

    # Ordina per data di certificazione decrescente
    certificazioni = query.order_by(CertificazioneCavo.data_certificazione.desc()).all()

    # Converte i risultati nel formato richiesto
    result = []
    for cert in certificazioni:
        result.append(CertificazioneCavoListResponse(
            id_certificazione=cert.id_certificazione,
            id_cavo=cert.id_cavo,
            numero_certificato=cert.numero_certificato,
            data_certificazione=cert.data_certificazione,
            id_operatore=cert.id_operatore,
            valore_isolamento=cert.valore_isolamento,
            strumento_utilizzato=cert.strumento_utilizzato,
            lunghezza_misurata=cert.lunghezza_misurata,
            cavo_tipologia=cert.cavo_tipologia,
            cavo_sezione=cert.cavo_sezione
        ))

    return result


@router.post("/{cantiere_id}/certificazioni", response_model=CertificazioneCavoResponse)
def create_certificazione(
    cantiere_id: int,
    certificazione_in: CertificazioneCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea una nuova certificazione per un cavo.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che il cavo esista e appartenga al cantiere
    cavo = db.query(Cavo).filter(
        Cavo.id_cavo == certificazione_in.id_cavo,
        Cavo.id_cantiere == cantiere_id
    ).first()

    if not cavo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cavo {certificazione_in.id_cavo} non trovato nel cantiere {cantiere_id}"
        )

    # Verifica che non esista già una certificazione per questo cavo
    existing_cert = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_cantiere == cantiere_id,
        CertificazioneCavo.id_cavo == certificazione_in.id_cavo
    ).first()

    if existing_cert:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Esiste già una certificazione per il cavo {certificazione_in.id_cavo}"
        )

    # Verifica che lo strumento esista se specificato
    if certificazione_in.id_strumento:
        strumento = db.query(StrumentoCertificato).filter(
            StrumentoCertificato.id_strumento == certificazione_in.id_strumento,
            StrumentoCertificato.id_cantiere == cantiere_id
        ).first()

        if not strumento:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Strumento con ID {certificazione_in.id_strumento} non trovato nel cantiere"
            )

    # Genera il numero di certificato
    numero_certificato = genera_numero_certificato(db, cantiere_id)

    # Usa la metratura reale del cavo come lunghezza misurata se non specificata
    lunghezza_misurata = certificazione_in.lunghezza_misurata
    if lunghezza_misurata is None:
        lunghezza_misurata = cavo.metratura_reale

    # Crea la certificazione
    certificazione = CertificazioneCavo(
        id_cantiere=cantiere_id,
        id_cavo=certificazione_in.id_cavo,
        numero_certificato=numero_certificato,
        data_certificazione=date.today(),
        id_operatore=certificazione_in.id_operatore,
        strumento_utilizzato=certificazione_in.strumento_utilizzato,
        id_strumento=certificazione_in.id_strumento,
        lunghezza_misurata=lunghezza_misurata,
        valore_continuita=certificazione_in.valore_continuita or "OK",
        valore_isolamento=certificazione_in.valore_isolamento or "500",
        valore_resistenza=certificazione_in.valore_resistenza or "OK",
        note=certificazione_in.note,
        timestamp_modifica=datetime.now()
    )

    db.add(certificazione)
    db.commit()
    db.refresh(certificazione)

    return certificazione


@router.get("/{cantiere_id}/certificazioni/{certificazione_id}", response_model=CertificazioneCavoResponse)
def get_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera i dettagli di una certificazione specifica.
    """
    # Query con join per ottenere tutte le informazioni
    query = db.query(
        CertificazioneCavo,
        Cavo.tipologia.label('cavo_tipologia'),
        Cavo.sezione.label('cavo_sezione'),
        Cavo.ubicazione_partenza.label('cavo_ubicazione_partenza'),
        Cavo.ubicazione_arrivo.label('cavo_ubicazione_arrivo'),
        Cavo.metri_teorici.label('cavo_metri_teorici'),
        Cavo.stato_installazione.label('cavo_stato_installazione'),
        StrumentoCertificato.nome.label('strumento_nome'),
        StrumentoCertificato.marca.label('strumento_marca'),
        StrumentoCertificato.modello.label('strumento_modello')
    ).join(
        Cavo, and_(
            CertificazioneCavo.id_cavo == Cavo.id_cavo,
            CertificazioneCavo.id_cantiere == Cavo.id_cantiere
        )
    ).outerjoin(
        StrumentoCertificato,
        CertificazioneCavo.id_strumento == StrumentoCertificato.id_strumento
    ).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    )

    result = query.first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    certificazione = result[0]

    # Crea la risposta con tutte le informazioni
    response = CertificazioneCavoResponse(
        id_certificazione=certificazione.id_certificazione,
        id_cantiere=certificazione.id_cantiere,
        id_cavo=certificazione.id_cavo,
        numero_certificato=certificazione.numero_certificato,
        data_certificazione=certificazione.data_certificazione,
        id_operatore=certificazione.id_operatore,
        strumento_utilizzato=certificazione.strumento_utilizzato,
        id_strumento=certificazione.id_strumento,
        lunghezza_misurata=certificazione.lunghezza_misurata,
        valore_continuita=certificazione.valore_continuita,
        valore_isolamento=certificazione.valore_isolamento,
        valore_resistenza=certificazione.valore_resistenza,
        percorso_certificato=certificazione.percorso_certificato,
        percorso_foto=certificazione.percorso_foto,
        note=certificazione.note,
        timestamp_creazione=certificazione.timestamp_creazione,
        timestamp_modifica=certificazione.timestamp_modifica,
        cavo_tipologia=result.cavo_tipologia,
        cavo_sezione=result.cavo_sezione,
        cavo_ubicazione_partenza=result.cavo_ubicazione_partenza,
        cavo_ubicazione_arrivo=result.cavo_ubicazione_arrivo,
        cavo_metri_teorici=result.cavo_metri_teorici,
        cavo_stato_installazione=result.cavo_stato_installazione,
        strumento_nome=result.strumento_nome,
        strumento_marca=result.strumento_marca,
        strumento_modello=result.strumento_modello
    )

    return response


@router.put("/{cantiere_id}/certificazioni/{certificazione_id}", response_model=CertificazioneCavoResponse)
def update_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    certificazione_update: CertificazioneCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna una certificazione esistente.
    """
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()

    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Verifica che lo strumento esista se viene modificato
    if certificazione_update.id_strumento:
        strumento = db.query(StrumentoCertificato).filter(
            StrumentoCertificato.id_strumento == certificazione_update.id_strumento,
            StrumentoCertificato.id_cantiere == cantiere_id
        ).first()

        if not strumento:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Strumento con ID {certificazione_update.id_strumento} non trovato nel cantiere"
            )

    # Aggiorna i campi
    update_data = certificazione_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(certificazione, field, value)

    certificazione.timestamp_modifica = datetime.now()

    db.commit()
    db.refresh(certificazione)

    return certificazione


@router.delete("/{cantiere_id}/certificazioni/{certificazione_id}")
def delete_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina una certificazione.
    """
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()

    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Elimina eventuali file associati (PDF, foto)
    # TODO: Implementare eliminazione file fisici se necessario

    db.delete(certificazione)
    db.commit()

    return {"message": "Certificazione eliminata con successo"}
