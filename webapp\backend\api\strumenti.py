from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Any
from datetime import datetime

from webapp.backend.database import get_db
from webapp.backend.models.user import User
from webapp.backend.models.cantiere import Cantiere
from webapp.backend.models.strumento_certificato import StrumentoCertificato
from webapp.backend.schemas.strumento_certificato import (
    StrumentoCertificatoCreate,
    StrumentoCertificatoUpdate,
    StrumentoCertificatoResponse
)
from webapp.backend.core.security import get_current_active_user

router = APIRouter()


@router.get("/{cantiere_id}/strumenti", response_model=List[StrumentoCertificatoResponse])
def get_strumenti_cantiere(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutti gli strumenti certificati di un cantiere.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Recupera gli strumenti del cantiere
    strumenti = db.query(StrumentoCertificato).filter(
        StrumentoCertificato.id_cantiere == cantiere_id
    ).order_by(StrumentoCertificato.nome, StrumentoCertificato.marca).all()

    return strumenti


@router.post("/{cantiere_id}/strumenti", response_model=StrumentoCertificatoResponse)
def create_strumento(
    cantiere_id: int,
    strumento_in: StrumentoCertificatoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea un nuovo strumento certificato.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che non esista già uno strumento con lo stesso numero di serie nel cantiere
    existing_strumento = db.query(StrumentoCertificato).filter(
        StrumentoCertificato.id_cantiere == cantiere_id,
        StrumentoCertificato.numero_serie == strumento_in.numero_serie
    ).first()

    if existing_strumento:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Uno strumento con numero di serie {strumento_in.numero_serie} esiste già nel cantiere"
        )

    # Crea il nuovo strumento
    strumento = StrumentoCertificato(
        id_cantiere=cantiere_id,
        nome=strumento_in.nome,
        marca=strumento_in.marca,
        modello=strumento_in.modello,
        numero_serie=strumento_in.numero_serie,
        data_calibrazione=strumento_in.data_calibrazione,
        data_scadenza_calibrazione=strumento_in.data_scadenza_calibrazione,
        certificato_calibrazione=strumento_in.certificato_calibrazione,
        note=strumento_in.note,
        timestamp_modifica=datetime.now()
    )

    db.add(strumento)
    db.commit()
    db.refresh(strumento)

    return strumento


@router.get("/{cantiere_id}/strumenti/{strumento_id}", response_model=StrumentoCertificatoResponse)
def get_strumento(
    cantiere_id: int,
    strumento_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera un singolo strumento certificato.
    """
    strumento = db.query(StrumentoCertificato).filter(
        StrumentoCertificato.id_strumento == strumento_id,
        StrumentoCertificato.id_cantiere == cantiere_id
    ).first()

    if not strumento:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Strumento con ID {strumento_id} non trovato nel cantiere {cantiere_id}"
        )

    return strumento


@router.put("/{cantiere_id}/strumenti/{strumento_id}", response_model=StrumentoCertificatoResponse)
def update_strumento(
    cantiere_id: int,
    strumento_id: int,
    strumento_update: StrumentoCertificatoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna un strumento certificato.
    """
    strumento = db.query(StrumentoCertificato).filter(
        StrumentoCertificato.id_strumento == strumento_id,
        StrumentoCertificato.id_cantiere == cantiere_id
    ).first()

    if not strumento:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Strumento con ID {strumento_id} non trovato nel cantiere {cantiere_id}"
        )

    # Verifica numero di serie univoco se viene modificato
    if strumento_update.numero_serie and strumento_update.numero_serie != strumento.numero_serie:
        existing_strumento = db.query(StrumentoCertificato).filter(
            StrumentoCertificato.id_cantiere == cantiere_id,
            StrumentoCertificato.numero_serie == strumento_update.numero_serie,
            StrumentoCertificato.id_strumento != strumento_id
        ).first()

        if existing_strumento:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Uno strumento con numero di serie {strumento_update.numero_serie} esiste già nel cantiere"
            )

    # Aggiorna i campi
    update_data = strumento_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(strumento, field, value)

    strumento.timestamp_modifica = datetime.now()

    db.commit()
    db.refresh(strumento)

    return strumento


@router.delete("/{cantiere_id}/strumenti/{strumento_id}")
def delete_strumento(
    cantiere_id: int,
    strumento_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina uno strumento certificato.
    """
    strumento = db.query(StrumentoCertificato).filter(
        StrumentoCertificato.id_strumento == strumento_id,
        StrumentoCertificato.id_cantiere == cantiere_id
    ).first()

    if not strumento:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Strumento con ID {strumento_id} non trovato nel cantiere {cantiere_id}"
        )

    # Verifica che lo strumento non sia utilizzato in certificazioni
    from webapp.backend.models.certificazione_cavo import CertificazioneCavo
    certificazioni_associate = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_strumento == strumento_id
    ).count()

    if certificazioni_associate > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Impossibile eliminare lo strumento: è utilizzato in {certificazioni_associate} certificazioni"
        )

    db.delete(strumento)
    db.commit()

    return {"message": "Strumento eliminato con successo"}
