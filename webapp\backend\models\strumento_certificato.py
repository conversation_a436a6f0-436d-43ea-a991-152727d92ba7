from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from webapp.backend.database import Base

class StrumentoCertificato(Base):
    """
    Modello SQLAlchemy per la tabella strumenti_certificati.
    Corrisponde alla tabella StrumentiCertificati nel database esistente.
    """
    __tablename__ = "strumenticertificati"

    id_strumento = Column(Integer, primary_key=True, index=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    nome = Column(String, nullable=False)
    marca = Column(String, nullable=False)
    modello = Column(String, nullable=False)
    numero_serie = Column(String, nullable=False)
    data_calibrazione = Column(Date, nullable=False)
    data_scadenza_calibrazione = Column(Date, nullable=False)
    certificato_calibrazione = Column(String, nullable=True)
    note = Column(Text, nullable=True)
    timestamp_creazione = Column(DateTime, default=func.now())
    timestamp_modifica = Column(DateTime, nullable=True)

    # Relazioni
    cantiere = relationship("Cantiere", backref="strumenti_certificati")
    certificazioni_cavi = relationship("CertificazioneCavo", back_populates="strumento_certificato")
