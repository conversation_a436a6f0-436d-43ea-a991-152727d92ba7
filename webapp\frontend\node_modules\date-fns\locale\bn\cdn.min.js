(()=>{var $;function Q(G){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},Q(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function O(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){z(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function z(G,H,J){if(H=W(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function W(G){var H=D(G,"string");return Q(H)=="symbol"?H:String(H)}function D(G,H){if(Q(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(Q(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var S=Object.defineProperty,ZG=function G(H,J){for(var X in J)S(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})};function q(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var U=G.defaultWidth,T=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[T]||G.values[U]}var I=G.argumentCallback?G.argumentCallback(H):H;return Y[I]}}function M(G,H){if(G>18&&G<=31)return H+"\u09B6\u09C7";else switch(G){case 1:return H+"\u09B2\u09BE";case 2:case 3:return H+"\u09B0\u09BE";case 4:return H+"\u09A0\u09BE";default:return H+"\u0987"}}function E(G){return G.toString().replace(/\d/g,function(H){return R.locale[H]})}var R={locale:{1:"\u09E7",2:"\u09E8",3:"\u09E9",4:"\u09EA",5:"\u09EB",6:"\u09EC",7:"\u09ED",8:"\u09EE",9:"\u09EF",0:"\u09E6"},number:{"\u09E7":"1","\u09E8":"2","\u09E9":"3","\u09EA":"4","\u09EB":"5","\u09EC":"6","\u09ED":"7","\u09EE":"8","\u09EF":"9","\u09E6":"0"}},V={narrow:["\u0996\u09CD\u09B0\u09BF\u0983\u09AA\u09C2\u0983","\u0996\u09CD\u09B0\u09BF\u0983"],abbreviated:["\u0996\u09CD\u09B0\u09BF\u0983\u09AA\u09C2\u09B0\u09CD\u09AC","\u0996\u09CD\u09B0\u09BF\u0983"],wide:["\u0996\u09CD\u09B0\u09BF\u09B8\u09CD\u099F\u09AA\u09C2\u09B0\u09CD\u09AC","\u0996\u09CD\u09B0\u09BF\u09B8\u09CD\u099F\u09BE\u09AC\u09CD\u09A6"]},L={narrow:["\u09E7","\u09E8","\u09E9","\u09EA"],abbreviated:["\u09E7\u09A4\u09CD\u09B0\u09C8","\u09E8\u09A4\u09CD\u09B0\u09C8","\u09E9\u09A4\u09CD\u09B0\u09C8","\u09EA\u09A4\u09CD\u09B0\u09C8"],wide:["\u09E7\u09AE \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995","\u09E8\u09DF \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995","\u09E9\u09DF \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995","\u09EA\u09B0\u09CD\u09A5 \u09A4\u09CD\u09B0\u09C8\u09AE\u09BE\u09B8\u09BF\u0995"]},j={narrow:["\u099C\u09BE\u09A8\u09C1","\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1","\u09AE\u09BE\u09B0\u09CD\u099A","\u098F\u09AA\u09CD\u09B0\u09BF\u09B2","\u09AE\u09C7","\u099C\u09C1\u09A8","\u099C\u09C1\u09B2\u09BE\u0987","\u0986\u0997\u09B8\u09CD\u099F","\u09B8\u09C7\u09AA\u09CD\u099F","\u0985\u0995\u09CD\u099F\u09CB","\u09A8\u09AD\u09C7","\u09A1\u09BF\u09B8\u09C7"],abbreviated:["\u099C\u09BE\u09A8\u09C1","\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1","\u09AE\u09BE\u09B0\u09CD\u099A","\u098F\u09AA\u09CD\u09B0\u09BF\u09B2","\u09AE\u09C7","\u099C\u09C1\u09A8","\u099C\u09C1\u09B2\u09BE\u0987","\u0986\u0997\u09B8\u09CD\u099F","\u09B8\u09C7\u09AA\u09CD\u099F","\u0985\u0995\u09CD\u099F\u09CB","\u09A8\u09AD\u09C7","\u09A1\u09BF\u09B8\u09C7"],wide:["\u099C\u09BE\u09A8\u09C1\u09DF\u09BE\u09B0\u09BF","\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1\u09DF\u09BE\u09B0\u09BF","\u09AE\u09BE\u09B0\u09CD\u099A","\u098F\u09AA\u09CD\u09B0\u09BF\u09B2","\u09AE\u09C7","\u099C\u09C1\u09A8","\u099C\u09C1\u09B2\u09BE\u0987","\u0986\u0997\u09B8\u09CD\u099F","\u09B8\u09C7\u09AA\u09CD\u099F\u09C7\u09AE\u09CD\u09AC\u09B0","\u0985\u0995\u09CD\u099F\u09CB\u09AC\u09B0","\u09A8\u09AD\u09C7\u09AE\u09CD\u09AC\u09B0","\u09A1\u09BF\u09B8\u09C7\u09AE\u09CD\u09AC\u09B0"]},w={narrow:["\u09B0","\u09B8\u09CB","\u09AE","\u09AC\u09C1","\u09AC\u09C3","\u09B6\u09C1","\u09B6"],short:["\u09B0\u09AC\u09BF","\u09B8\u09CB\u09AE","\u09AE\u0999\u09CD\u0997\u09B2","\u09AC\u09C1\u09A7","\u09AC\u09C3\u09B9","\u09B6\u09C1\u0995\u09CD\u09B0","\u09B6\u09A8\u09BF"],abbreviated:["\u09B0\u09AC\u09BF","\u09B8\u09CB\u09AE","\u09AE\u0999\u09CD\u0997\u09B2","\u09AC\u09C1\u09A7","\u09AC\u09C3\u09B9","\u09B6\u09C1\u0995\u09CD\u09B0","\u09B6\u09A8\u09BF"],wide:["\u09B0\u09AC\u09BF\u09AC\u09BE\u09B0","\u09B8\u09CB\u09AE\u09AC\u09BE\u09B0","\u09AE\u0999\u09CD\u0997\u09B2\u09AC\u09BE\u09B0","\u09AC\u09C1\u09A7\u09AC\u09BE\u09B0","\u09AC\u09C3\u09B9\u09B8\u09CD\u09AA\u09A4\u09BF\u09AC\u09BE\u09B0 ","\u09B6\u09C1\u0995\u09CD\u09B0\u09AC\u09BE\u09B0","\u09B6\u09A8\u09BF\u09AC\u09BE\u09B0"]},_={narrow:{am:"\u09AA\u09C2",pm:"\u0985\u09AA",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},abbreviated:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},wide:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"}},f={narrow:{am:"\u09AA\u09C2",pm:"\u0985\u09AA",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},abbreviated:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"},wide:{am:"\u09AA\u09C2\u09B0\u09CD\u09AC\u09BE\u09B9\u09CD\u09A8",pm:"\u0985\u09AA\u09B0\u09BE\u09B9\u09CD\u09A8",midnight:"\u09AE\u09A7\u09CD\u09AF\u09B0\u09BE\u09A4",noon:"\u09AE\u09A7\u09CD\u09AF\u09BE\u09B9\u09CD\u09A8",morning:"\u09B8\u0995\u09BE\u09B2",afternoon:"\u09AC\u09BF\u0995\u09BE\u09B2",evening:"\u09B8\u09A8\u09CD\u09A7\u09CD\u09AF\u09BE",night:"\u09B0\u09BE\u09A4"}},F=function G(H,J){var X=Number(H),Y=E(X),Z=J===null||J===void 0?void 0:J.unit;if(Z==="date")return M(X,Y);if(X>10||X===0)return Y+"\u09A4\u09AE";var B=X%10;switch(B){case 2:case 3:return Y+"\u09DF";case 4:return Y+"\u09B0\u09CD\u09A5";case 6:return Y+"\u09B7\u09CD\u09A0";default:return Y+"\u09AE"}},N={ordinalNumber:F,era:q({values:V,defaultWidth:"wide"}),quarter:q({values:L,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:q({values:j,defaultWidth:"wide"}),day:q({values:w,defaultWidth:"wide"}),dayPeriod:q({values:_,defaultWidth:"wide",formattingValues:f,defaultFormattingWidth:"wide"})},v={lessThanXSeconds:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1"},xSeconds:{one:"\u09E7 \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1",other:"{{count}} \u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1"},halfAMinute:"\u0986\u09A7 \u09AE\u09BF\u09A8\u09BF\u099F",lessThanXMinutes:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AE\u09BF\u09A8\u09BF\u099F",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AE\u09BF\u09A8\u09BF\u099F"},xMinutes:{one:"\u09E7 \u09AE\u09BF\u09A8\u09BF\u099F",other:"{{count}} \u09AE\u09BF\u09A8\u09BF\u099F"},aboutXHours:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u0998\u09A8\u09CD\u099F\u09BE",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u0998\u09A8\u09CD\u099F\u09BE"},xHours:{one:"\u09E7 \u0998\u09A8\u09CD\u099F\u09BE",other:"{{count}} \u0998\u09A8\u09CD\u099F\u09BE"},xDays:{one:"\u09E7 \u09A6\u09BF\u09A8",other:"{{count}} \u09A6\u09BF\u09A8"},aboutXWeeks:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9"},xWeeks:{one:"\u09E7 \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9",other:"{{count}} \u09B8\u09AA\u09CD\u09A4\u09BE\u09B9"},aboutXMonths:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AE\u09BE\u09B8",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AE\u09BE\u09B8"},xMonths:{one:"\u09E7 \u09AE\u09BE\u09B8",other:"{{count}} \u09AE\u09BE\u09B8"},aboutXYears:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AC\u099B\u09B0",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AC\u099B\u09B0"},xYears:{one:"\u09E7 \u09AC\u099B\u09B0",other:"{{count}} \u09AC\u099B\u09B0"},overXYears:{one:"\u09E7 \u09AC\u099B\u09B0\u09C7\u09B0 \u09AC\u09C7\u09B6\u09BF",other:"{{count}} \u09AC\u099B\u09B0\u09C7\u09B0 \u09AC\u09C7\u09B6\u09BF"},almostXYears:{one:"\u09AA\u09CD\u09B0\u09BE\u09DF \u09E7 \u09AC\u099B\u09B0",other:"\u09AA\u09CD\u09B0\u09BE\u09DF {{count}} \u09AC\u099B\u09B0"}},P=function G(H,J,X){var Y,Z=v[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",E(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y+" \u098F\u09B0 \u09AE\u09A7\u09CD\u09AF\u09C7";else return Y+" \u0986\u0997\u09C7";return Y};function A(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var k={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},h={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},y={full:"{{date}} {{time}} '\u09B8\u09AE\u09DF'",long:"{{date}} {{time}} '\u09B8\u09AE\u09DF'",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},b={date:A({formats:k,defaultWidth:"full"}),time:A({formats:h,defaultWidth:"full"}),dateTime:A({formats:y,defaultWidth:"full"})},c={lastWeek:"'\u0997\u09A4' eeee '\u09B8\u09AE\u09DF' p",yesterday:"'\u0997\u09A4\u0995\u09BE\u09B2' '\u09B8\u09AE\u09DF' p",today:"'\u0986\u099C' '\u09B8\u09AE\u09DF' p",tomorrow:"'\u0986\u0997\u09BE\u09AE\u09C0\u0995\u09BE\u09B2' '\u09B8\u09AE\u09DF' p",nextWeek:"eeee '\u09B8\u09AE\u09DF' p",other:"P"},g=function G(H,J,X,Y){return c[H]};function C(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var B=Z[0],U=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],T=Array.isArray(U)?m(U,function(K){return K.test(B)}):d(U,function(K){return K.test(B)}),I;I=G.valueCallback?G.valueCallback(T):T,I=J.valueCallback?J.valueCallback(I):I;var YG=H.slice(B.length);return{value:I,rest:YG}}}function d(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function m(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function p(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var U=H.slice(Y.length);return{value:B,rest:U}}}var u=/^(\d+)(ম|য়|র্থ|ষ্ঠ|শে|ই|তম)?/i,l=/\d+/i,i={narrow:/^(খ্রিঃপূঃ|খ্রিঃ)/i,abbreviated:/^(খ্রিঃপূর্ব|খ্রিঃ)/i,wide:/^(খ্রিস্টপূর্ব|খ্রিস্টাব্দ)/i},n={narrow:[/^খ্রিঃপূঃ/i,/^খ্রিঃ/i],abbreviated:[/^খ্রিঃপূর্ব/i,/^খ্রিঃ/i],wide:[/^খ্রিস্টপূর্ব/i,/^খ্রিস্টাব্দ/i]},s={narrow:/^[১২৩৪]/i,abbreviated:/^[১২৩৪]ত্রৈ/i,wide:/^[১২৩৪](ম|য়|র্থ)? ত্রৈমাসিক/i},o={any:[/১/i,/২/i,/৩/i,/৪/i]},r={narrow:/^(জানু|ফেব্রু|মার্চ|এপ্রিল|মে|জুন|জুলাই|আগস্ট|সেপ্ট|অক্টো|নভে|ডিসে)/i,abbreviated:/^(জানু|ফেব্রু|মার্চ|এপ্রিল|মে|জুন|জুলাই|আগস্ট|সেপ্ট|অক্টো|নভে|ডিসে)/i,wide:/^(জানুয়ারি|ফেব্রুয়ারি|মার্চ|এপ্রিল|মে|জুন|জুলাই|আগস্ট|সেপ্টেম্বর|অক্টোবর|নভেম্বর|ডিসেম্বর)/i},a={any:[/^জানু/i,/^ফেব্রু/i,/^মার্চ/i,/^এপ্রিল/i,/^মে/i,/^জুন/i,/^জুলাই/i,/^আগস্ট/i,/^সেপ্ট/i,/^অক্টো/i,/^নভে/i,/^ডিসে/i]},t={narrow:/^(র|সো|ম|বু|বৃ|শু|শ)+/i,short:/^(রবি|সোম|মঙ্গল|বুধ|বৃহ|শুক্র|শনি)+/i,abbreviated:/^(রবি|সোম|মঙ্গল|বুধ|বৃহ|শুক্র|শনি)+/i,wide:/^(রবিবার|সোমবার|মঙ্গলবার|বুধবার|বৃহস্পতিবার |শুক্রবার|শনিবার)+/i},e={narrow:[/^র/i,/^সো/i,/^ম/i,/^বু/i,/^বৃ/i,/^শু/i,/^শ/i],short:[/^রবি/i,/^সোম/i,/^মঙ্গল/i,/^বুধ/i,/^বৃহ/i,/^শুক্র/i,/^শনি/i],abbreviated:[/^রবি/i,/^সোম/i,/^মঙ্গল/i,/^বুধ/i,/^বৃহ/i,/^শুক্র/i,/^শনি/i],wide:[/^রবিবার/i,/^সোমবার/i,/^মঙ্গলবার/i,/^বুধবার/i,/^বৃহস্পতিবার /i,/^শুক্রবার/i,/^শনিবার/i]},GG={narrow:/^(পূ|অপ|মধ্যরাত|মধ্যাহ্ন|সকাল|বিকাল|সন্ধ্যা|রাত)/i,abbreviated:/^(পূর্বাহ্ন|অপরাহ্ন|মধ্যরাত|মধ্যাহ্ন|সকাল|বিকাল|সন্ধ্যা|রাত)/i,wide:/^(পূর্বাহ্ন|অপরাহ্ন|মধ্যরাত|মধ্যাহ্ন|সকাল|বিকাল|সন্ধ্যা|রাত)/i},HG={any:{am:/^পূ/i,pm:/^অপ/i,midnight:/^মধ্যরাত/i,noon:/^মধ্যাহ্ন/i,morning:/সকাল/i,afternoon:/বিকাল/i,evening:/সন্ধ্যা/i,night:/রাত/i}},JG={ordinalNumber:p({matchPattern:u,parsePattern:l,valueCallback:function G(H){return parseInt(H,10)}}),era:C({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"wide"}),quarter:C({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:C({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),day:C({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"wide"}),dayPeriod:C({matchPatterns:GG,defaultMatchWidth:"wide",parsePatterns:HG,defaultParseWidth:"any"})},XG={code:"bn",formatDistance:P,formatLong:b,formatRelative:g,localize:N,match:JG,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=O(O({},window.dateFns),{},{locale:O(O({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{bn:XG})})})();

//# debugId=C9444566F991515A64756E2164756E21
