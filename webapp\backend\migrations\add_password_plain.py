import sys
import os
import logging

# Aggiungi la directory principale al path per importare i moduli
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from webapp.backend.database import get_db
from sqlalchemy import text

def migrate():
    """
    Aggiunge la colonna password_plain alla tabella utenti.
    """
    try:
        # Ottieni una connessione al database
        db = next(get_db())

        # Verifica se la colonna esiste già
        result = db.execute(text("SELECT column_name FROM information_schema.columns WHERE table_name = 'utenti' AND column_name = 'password_plain'"))
        if result.fetchone():
            print("La colonna password_plain esiste già nella tabella utenti.")
            return

        # Aggiungi la colonna password_plain
        db.execute(text("ALTER TABLE utenti ADD COLUMN password_plain TEXT"))

        # Commit delle modifiche
        db.commit()

        print("Colonna password_plain aggiunta con successo alla tabella utenti.")
    except Exception as e:
        print(f"Errore durante la migrazione: {str(e)}")
        db.rollback()

if __name__ == "__main__":
    migrate()
