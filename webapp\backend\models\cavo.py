from sqlalchemy import Column, Integer, String, Float, DateTime, Foreign<PERSON>ey, <PERSON>olean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from webapp.backend.database import Base

class Cavo(Base):
    """
    Modello SQLAlchemy per la tabella cavi.
    Corrisponde alla tabella cavi nel database esistente.
    """
    __tablename__ = "cavi"

    id_cavo = Column(String, primary_key=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), primary_key=True)
    revisione_ufficiale = Column(String, nullable=False)
    sistema = Column(String, nullable=True)
    utility = Column(String, nullable=True)
    colore_cavo = Column(String, nullable=True)
    tipologia = Column(String, nullable=True)
    n_conduttori = Column(String, nullable=True)
    sezione = Column(String, nullable=True)
    # Rinominato da SH a sh per corrispondere alla struttura del database
    sh = Column(String, nullable=True)
    ubicazione_partenza = Column(String, nullable=True)
    utenza_partenza = Column(String, nullable=True)
    descrizione_utenza_partenza = Column(String, nullable=True)
    ubicazione_arrivo = Column(String, nullable=True)
    utenza_arrivo = Column(String, nullable=True)
    descrizione_utenza_arrivo = Column(String, nullable=True)
    metri_teorici = Column(Float, nullable=True)
    metratura_reale = Column(Float, nullable=True, default=0)
    responsabile_posa = Column(String, nullable=True)
    id_bobina = Column(String, ForeignKey("parco_cavi.id_bobina"), nullable=True)
    stato_installazione = Column(String, nullable=True)
    modificato_manualmente = Column(Integer, default=0)
    timestamp = Column(DateTime, default=func.now())
    collegamenti = Column(Integer, default=0)
    responsabile_partenza = Column(String, nullable=True)
    responsabile_arrivo = Column(String, nullable=True)
    comanda_posa = Column(String, nullable=True)
    comanda_partenza = Column(String, nullable=True)
    comanda_arrivo = Column(String, nullable=True)

    # Relazioni
    cantiere = relationship("Cantiere", back_populates="cavi")
    # Aggiungi altre relazioni se necessario
